//+------------------------------------------------------------------+
//|                                          ZigZagSimple.mq4        |
//|                                          Copyright 2023           |
//|                                                                   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023"
#property link      ""
#property version   "1.00"
#property strict
#property indicator_chart_window

// 输入参数
input int    ZigZag_Depth=12;     // ZigZag深度
input int    ZigZag_Deviation=5;   // ZigZag偏差
input int    ZigZag_Backstep=3;    // ZigZag回溯
input color  PointColor=clrRed;    // 转折点颜色
input int    PointSize=10;         // 点的大小
input int    StartBar=4;           // 从第几根K线开始查找(默认第4根)
input color  BullishColor=clrLime; // 看涨趋势颜色
input color  BearishColor=clrRed;  // 看跌趋势颜色
input int    FontSize=10;          // 文字大小
input bool   ShowFiboLines=true;   // 显示斐波那契线
input color  FiboLineColor=clrGold;// 斐波那契线颜色
input int    FiboLineStyle=STYLE_DOT; // 斐波那契线样式
input int    FiboLineWidth=1;      // 斐波那契线宽度

// 斐波那契档位设置
input double Fibo_Level0=-0.3;     // 第1档
input double Fibo_Level1=0.0;      // 第2档
input double Fibo_Level2=0.236;      // 第3档
input double Fibo_Level3=0.382;     // 第4档
input double Fibo_Level4=0.5;     // 第5档
input double Fibo_Level5=0.618;     // 第6档
input double Fibo_Level6=0.786;     // 第7档
input double Fibo_Level7=0.876;     // 第8档
input double Fibo_Level8=1.0;      // 第9档
input double Fibo_Level9=1.25;     // 第10档
input double Fibo_Level10=1.5;     // 第11档

// 交易参数
input bool   EnableTrading=false;  // 启用自动交易
input bool   TestMode=false;       // 回测模式(回测时启用可提高速度)
input double LotSize=0.1;          // 固定仓位大小
input int    MagicNumber=12345;    // 魔术编号
input int    Slippage=3;           // 允许滑点(点)
input int    DefaultTP=2000;       // 默认止盈(点数)
input int    SLOffset=50;          // 止损偏移(点数)
input bool   EnableTrailingStop=false; // 启用移动止损
input int    TrailingOffset=44;    // 移动止损偏移(点数)
input bool   EnableBreakEven=false; // 启用止损触发回本功能
input bool   EnableBreakEvenTP=true; // 启用回本单止盈

// 初始下单挡位设置
input double D_Value_SuperLow=5.0; // 超低d值下限(5<d<15时交易)
input double D_Value_VeryLow=15.0; // 极低d值下限(15<d<20时交易)
input double D_Value_Low=20.0;     // d值下限(小于此值不交易)
input double D_Value_Mid=30.0;     // d值中值(分隔第五档和第四档)
input double D_Value_High=40.0;    // d值上限(分隔第四档和第三档)
input int    Initial_Level_SuperLow=6; // 超低d值时的初始档位(数字加一，默认七)
input int    Initial_Level_VeryLow=5; // 极低d值时的初始档位(数字加一，默认六)
input int    Initial_Level_Low=4;  // d值>低值时的初始档位(数字加一，默认五)
input int    Initial_Level_Mid=3;  // d值>中值时的初始档位(数字加一，默认四)
input int    Initial_Level_High=2; // d值>高值时的初始档位(数字加一，默认三)


// 斐波那契比例数组(将在OnInit中初始化)
double fiboLevels[11];

// 全局变量
datetime lastTurningPoints[3] = {0, 0, 0};  // 存储最近三个转折点的时间
double lastTurningPrices[3] = {0, 0, 0};    // 存储最近三个转折点的价格
string trendDirection = "";                  // 趋势方向
double d1 = 0, d2 = 0, d = 0;               // 差值计算结果
int dSource = 0;                            // d值来源(1=d1, 2=d2)
double startPrice = 0, endPrice = 0;        // 斐波那契线的起点和终点价格

// 交易相关变量
int initialLevel = 0;                       // 初始下单挡位(根据d值判断)
bool hasOpenPosition = false;               // 是否有持仓
datetime lastDChange = 0;                   // 最后一次d值变化的时间
datetime lastTrailingCheck = 0;             // 最后一次移动止损检查时间

// 回本功能相关变量
double originalStopLoss = 0;                // 首单原始止损价格
double originalOpenPrice = 0;               // 首单开仓价格
double originalLotSize = 0;                 // 首单手数
int originalOrderType = -1;                 // 首单类型(OP_BUY或OP_SELL)
bool isBreakEvenOrder = false;              // 当前是否为回本单

// 添加以下全局变量，用于记忆已交易的价格区间
#define MAX_PRICE_MEMORY 20  // 最多记忆20个价格区间
double tradedPriceZones[MAX_PRICE_MEMORY][2]; // [0]=起始价格, [1]=结束价格
int tradedPriceZonesCount = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
   // 确保EA运行在图表窗口
   ChartSetInteger(0, CHART_FOREGROUND, false);
   
   // 初始化交易区域记录
   ClearTradedPriceZones();
   
   // 初始化斐波那契比例数组
   fiboLevels[0] = Fibo_Level0;
   fiboLevels[1] = Fibo_Level1;
   fiboLevels[2] = Fibo_Level2;
   fiboLevels[3] = Fibo_Level3;
   fiboLevels[4] = Fibo_Level4;
   fiboLevels[5] = Fibo_Level5;
   fiboLevels[6] = Fibo_Level6;
   fiboLevels[7] = Fibo_Level7;
   fiboLevels[8] = Fibo_Level8;
   fiboLevels[9] = Fibo_Level9;
   fiboLevels[10] = Fibo_Level10;
   
   // 验证斐波那契水平是否按升序排列
   for(int i=0; i<10; i++)
   {
      if(fiboLevels[i] >= fiboLevels[i+1])
      {
         Print("错误: 斐波那契水平必须按升序排列! 第", i+1, "档(", fiboLevels[i], ")>=第", i+2, "档(", fiboLevels[i+1], ")");
         return(INIT_PARAMETERS_INCORRECT);
      }
   }
   
   // 验证初始下单档位设置是否有效
   if(Initial_Level_Low < 0 || Initial_Level_Low > 7 ||
      Initial_Level_Mid < 0 || Initial_Level_Mid > 7 ||
      Initial_Level_High < 0 || Initial_Level_High > 7)
   {
      Print("错误: 初始下单档位必须在0-7之间!");
      return(INIT_PARAMETERS_INCORRECT);
   }
   
   // 验证d值阈值设置是否有效
   if(D_Value_Low <= 0 || D_Value_Mid <= D_Value_Low || D_Value_High <= D_Value_Mid)
   {
      Print("错误: d值阈值必须满足: D_Value_Low > 0, D_Value_Mid > D_Value_Low, D_Value_High > D_Value_Mid");
      return(INIT_PARAMETERS_INCORRECT);
   }
   
   // 输出斐波那契档位设置
   if(!TestMode)
   {
      Print("斐波那契档位设置:");
      for(int i=0; i<10; i++)
      {
         Print("第", i+1, "档: ", fiboLevels[i], "-", fiboLevels[i+1]);
      }
   }
   
   // 输出初始下单档位设置
   if(!TestMode)
   {
      Print("初始下单档位设置:");
      Print("d值 > ", D_Value_High, " -> 第", Initial_Level_High+1, "档");
      Print(D_Value_Mid, " < d值 <= ", D_Value_High, " -> 第", Initial_Level_Mid+1, "档");
      Print(D_Value_Low, " < d值 <= ", D_Value_Mid, " -> 第", Initial_Level_Low+1, "档");
      Print("d值 <= ", D_Value_Low, " -> 不交易");
   }
   
   // 初始化时立即查找转折点
   FindZigZagTurningPoints();
   AnalyzeTrend();
   if(!TestMode || IsVisualMode())
      MarkTurningPoints();
   
   // 初始化交易状态
   ResetTradeStatus();
   DetermineInitialLevel();
   
   // 输出EA启动信息
   if(!TestMode)
      Print("ZigZag简化交易EA已启动，交易功能: ", (EnableTrading ? "已启用" : "未启用"));
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 回测模式非可视化时跳过清除图形对象
   if(!TestMode || IsVisualMode())
   {
      // 清除图表上所有由EA创建的对象
      ObjectsDeleteAll(0, "ZigZagPoint_");
      ObjectsDeleteAll(0, "ZigZagLabel_");
      ObjectsDeleteAll(0, "ZigZagLine_");
      ObjectsDeleteAll(0, "TrendInfo");
      ObjectsDeleteAll(0, "TrendInfo2"); // 添加新标签的删除
      ObjectsDeleteAll(0, "FiboLine_");
      ObjectsDeleteAll(0, "FiboLabel_");
      ObjectsDeleteAll(0, "FiboInfo");
      ObjectsDeleteAll(0, "FiboWaveLine");
      ObjectsDeleteAll(0, "FiboPriceLabel_");
      ObjectsDeleteAll(0, "FiboStartLabel_");
      ObjectsDeleteAll(0, "TradeInfo");
      ObjectsDeleteAll(0, "LevelLabel_");
      ObjectsDeleteAll(0, "InitialOrderLevel");
      ObjectsDeleteAll(0, "InitialLevelLabel");
   }
   
   // 清除交易记录区域
   ClearTradedPriceZones();
   
   // 输出EA停止信息
   if(!TestMode)
      Print("ZigZag简化交易EA已停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   // 只在新K线出现时或间隔一定时间进行ZigZag点的更新和趋势分析
   static datetime lastBarTime = 0;
   static datetime lastAnalysisTime = 0;
   static double lastD = 0;
   static double lastStartPrice = 0;
   static double lastEndPrice = 0;
   
   datetime currentBarTime = Time[0];
   datetime currentTime = TimeCurrent();
   
   // 新K线检查
   bool isNewBar = (currentBarTime != lastBarTime);
   // 回测模式下只在新K线时更新，实盘/可视化模式下每10秒更新一次
   bool needUpdate = isNewBar || (!TestMode && currentTime - lastAnalysisTime > 10);
   
   if(isNewBar || needUpdate)
   {
      if(isNewBar)
         lastBarTime = currentBarTime;
      
      lastAnalysisTime = currentTime;
      
      // 保存上次的重要数据
      lastD = d;
      lastStartPrice = startPrice;
      lastEndPrice = endPrice;
      
      // 保存上次d值用于检测变化
      double prevD = d;
      int prevDSource = dSource;
      
      // 查找最近的三个ZigZag转折点
      FindZigZagTurningPoints();
      
      // 分析趋势
      AnalyzeTrend();
      
      // 在图表上标记转折点 - 回测非可视化模式下跳过
      if(!TestMode || IsVisualMode())
         MarkTurningPoints();

      
      // 检查d值是否发生明显变化
      if(MathAbs(d - prevD) / prevD > 0.05 || dSource != prevDSource || d == 0)
      {
         // d值变化超过5%或者源变化，重置交易状态
         ResetTradeStatus();
         if(!TestMode)
            Print("d值明显变化: ", prevD, " -> ", d, ", dSource: ", prevDSource, " -> ", dSource);
         
         // 重新确定初始下单挡位
         DetermineInitialLevel();
         
         // 如果价格区间明显变化，清除交易区域记录
         if(MathAbs(startPrice - lastStartPrice) > Point * 20 || MathAbs(endPrice - lastEndPrice) > Point * 20)
         {
            ClearTradedPriceZones();
         }
      }
      else if(d > 0 && initialLevel < 0)
      {
         // 如果有有效的d值但初始下单挡位无效，重新确定初始下单挡位
         if(!TestMode)
            Print("有效d值但初始下单挡位无效，重新确定初始下单挡位");
         DetermineInitialLevel();
      }
   }
   
   // 处理交易逻辑（仅当启用交易且d值有效）- 每个报价都检查
   if(EnableTrading && d > 0)
   {
      // 检查是否需要进行交易
      ManageSimpleTrading();
   }

   // 处理移动止损（每个新K线检查一次）
   if(EnableTrailingStop && hasOpenPosition && isNewBar && Time[0] != lastTrailingCheck)
   {
      CheckTrailingStop();
      lastTrailingCheck = Time[0];
   }

   // 检查止损触发回本功能（每个tick检查）
   if(EnableBreakEven && hasOpenPosition && originalStopLoss != 0 && !isBreakEvenOrder)
   {
      CheckStopLossTriggered();
   }
}

//+------------------------------------------------------------------+
//| 查找最近的三个ZigZag转折点                                        |
//+------------------------------------------------------------------+
void FindZigZagTurningPoints()
{
   int foundPoints = 0;
   int shift = StartBar - 1; // 从指定的K线开始查找（减1是因为数组索引从0开始）

   // 清除旧数据
   for(int i = 0; i < 3; i++)
   {
      lastTurningPoints[i] = 0;
      lastTurningPrices[i] = 0;
   }

   while(foundPoints < 3 && shift < 1000) // 限制搜索范围，避免无限循环
   {
      double zigzagValue = iCustom(NULL, 0, "ZigZag", ZigZag_Depth, ZigZag_Deviation, ZigZag_Backstep, 0, shift);

      // 如果ZigZag值不为0，则找到了一个转折点
      if(zigzagValue != 0 && zigzagValue != EMPTY_VALUE)
      {
         lastTurningPoints[foundPoints] = Time[shift];
         lastTurningPrices[foundPoints] = zigzagValue;
         foundPoints++;
      }

      shift++;
   }

   // 如果没有找到足够的转折点，输出日志
   if(foundPoints < 3 && !TestMode)
   {
      Print("警告: 只找到 ", foundPoints, " 个ZigZag转折点");
   }
}

//+------------------------------------------------------------------+
//| 分析趋势                                                          |
//+------------------------------------------------------------------+
void AnalyzeTrend()
{
   // 确保有足够的转折点进行分析
   if(lastTurningPoints[0] == 0 || lastTurningPoints[1] == 0 || lastTurningPoints[2] == 0)
   {
      trendDirection = "无法判断趋势 - 转折点不足";
      dSource = 0;
      return;
   }

   // 计算差值的绝对值
   d1 = MathAbs(lastTurningPrices[0] - lastTurningPrices[1]);
   d2 = MathAbs(lastTurningPrices[1] - lastTurningPrices[2]);

   // 找出最大差值
   d = MathMax(d1, d2);

   // 判断趋势
   if(d == d1) // 最大差值来源于z1和z2之间
   {
      dSource = 1;
      startPrice = lastTurningPrices[1]; // 起点为z2
      endPrice = lastTurningPrices[0];   // 终点为z1

      if(lastTurningPrices[0] > lastTurningPrices[1])
      {
         trendDirection = "看涨趋势";
      }
      else
      {
         trendDirection = "看跌趋势";
      }
   }
   else // 最大差值来源于z2和z3之间
   {
      dSource = 2;
      startPrice = lastTurningPrices[2]; // 起点为z3
      endPrice = lastTurningPrices[1];   // 终点为z2

      if(lastTurningPrices[1] > lastTurningPrices[2])
      {
         trendDirection = "看涨趋势";
      }
      else
      {
         trendDirection = "看跌趋势";
      }
   }
}

//+------------------------------------------------------------------+
//| 在图表上标记转折点                                                |
//+------------------------------------------------------------------+
void MarkTurningPoints()
{
   // 回测模式非可视化时跳过绘图
   if(TestMode && !IsVisualMode()) return;

   // 清除之前的标记
   ObjectsDeleteAll(0, "ZigZagPoint_");
   ObjectsDeleteAll(0, "ZigZagLabel_");
   ObjectsDeleteAll(0, "ZigZagLine_");
   ObjectsDeleteAll(0, "TrendInfo");
   ObjectsDeleteAll(0, "TrendInfo2");
   ObjectsDeleteAll(0, "FiboLine_");
   ObjectsDeleteAll(0, "FiboLabel_");
   ObjectsDeleteAll(0, "FiboStartLabel_");
   ObjectsDeleteAll(0, "FiboInfo");
   ObjectsDeleteAll(0, "FiboWaveLine");
   ObjectsDeleteAll(0, "TradeInfo");
   ObjectsDeleteAll(0, "InitialOrderLevel");
   ObjectsDeleteAll(0, "InitialLevelLabel");

   // 标记找到的转折点
   for(int i = 0; i < 3; i++)
   {
      if(lastTurningPoints[i] == 0) continue; // 跳过未找到的点

      string objName = "ZigZagPoint_" + IntegerToString(i);
      string pointName = "z" + IntegerToString(i+1); // 命名为z1, z2, z3

      // 创建箭头对象
      if(ObjectCreate(0, objName, OBJ_ARROW, 0, lastTurningPoints[i], lastTurningPrices[i]))
      {
         // 设置箭头的属性
         ObjectSetInteger(0, objName, OBJPROP_ARROWCODE, 159); // 圆形标记
         ObjectSetInteger(0, objName, OBJPROP_COLOR, PointColor);
         ObjectSetInteger(0, objName, OBJPROP_WIDTH, PointSize);
         ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
         ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1000); // 确保显示在最前面
      }

      // 添加价格标签和点名称
      string labelName = "ZigZagLabel_" + IntegerToString(i);
      if(ObjectCreate(0, labelName, OBJ_TEXT, 0, lastTurningPoints[i], lastTurningPrices[i]))
      {
         string priceText = pointName + ": " + DoubleToString(lastTurningPrices[i], Digits);
         ObjectSetString(0, labelName, OBJPROP_TEXT, priceText);
         ObjectSetInteger(0, labelName, OBJPROP_COLOR, PointColor);
         ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
         ObjectSetInteger(0, labelName, OBJPROP_SELECTABLE, false);
         // 将标签向上偏移一些，避免与点重叠
         ObjectSetDouble(0, labelName, OBJPROP_ANGLE, 0);
         ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
      }
   }

   // 连接转折点
   if(lastTurningPoints[0] != 0 && lastTurningPoints[1] != 0)
   {
      string lineName = "ZigZagLine_0_1";
      ObjectCreate(0, lineName, OBJ_TREND, 0, lastTurningPoints[0], lastTurningPrices[0],
                                             lastTurningPoints[1], lastTurningPrices[1]);
      ObjectSetInteger(0, lineName, OBJPROP_COLOR, PointColor);
      ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, lineName, OBJPROP_STYLE, STYLE_DASH);
      ObjectSetInteger(0, lineName, OBJPROP_RAY_RIGHT, false);
   }

   if(lastTurningPoints[1] != 0 && lastTurningPoints[2] != 0)
   {
      string lineName = "ZigZagLine_1_2";
      ObjectCreate(0, lineName, OBJ_TREND, 0, lastTurningPoints[1], lastTurningPrices[1],
                                             lastTurningPoints[2], lastTurningPrices[2]);
      ObjectSetInteger(0, lineName, OBJPROP_COLOR, PointColor);
      ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, lineName, OBJPROP_STYLE, STYLE_DASH);
      ObjectSetInteger(0, lineName, OBJPROP_RAY_RIGHT, false);
   }

   // 绘制斐波那契线
   if(ShowFiboLines && dSource > 0)
   {
      DrawFibonacciLines();
   }

   // 在图表左上角显示趋势信息
   string infoName = "TrendInfo";
   if(ObjectCreate(0, infoName, OBJ_LABEL, 0, 0, 0))
   {
      // 构建显示文本 - 第一部分：趋势和价格
      string infoText = "趋势: " + trendDirection + "\n";
      infoText += "z1:" + DoubleToString(lastTurningPrices[0], Digits) + " ";
      infoText += "z2:" + DoubleToString(lastTurningPrices[1], Digits) + " ";
      infoText += "z3:" + DoubleToString(lastTurningPrices[2], Digits);

      // 设置标签属性
      ObjectSetString(0, infoName, OBJPROP_TEXT, infoText);
      ObjectSetInteger(0, infoName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(0, infoName, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, infoName, OBJPROP_YDISTANCE, 15);
      ObjectSetInteger(0, infoName, OBJPROP_FONTSIZE, FontSize);

      // 根据趋势设置颜色
      if(trendDirection == "看涨趋势")
         ObjectSetInteger(0, infoName, OBJPROP_COLOR, BullishColor);
      else if(trendDirection == "看跌趋势")
         ObjectSetInteger(0, infoName, OBJPROP_COLOR, BearishColor);
      else
         ObjectSetInteger(0, infoName, OBJPROP_COLOR, clrWhite);
   }

   // 创建第二个标签，显示d值和初始下单挡位信息
   string infoName2 = "TrendInfo2";
   if(ObjectCreate(0, infoName2, OBJ_LABEL, 0, 0, 0))
   {
      // 构建显示文本 - 第二部分：d值和初始下单挡位
      string infoText = "d1=" + DoubleToString(d1, 1) + " d2=" + DoubleToString(d2, 1);
      infoText += " d=" + DoubleToString(d, 1) + "(d" + IntegerToString(dSource) + ")\n";
      infoText += "初始挡位: " + (initialLevel >= 0 ? "第" + IntegerToString(initialLevel+1) + "档" : "无");

      // 设置标签属性
      ObjectSetString(0, infoName2, OBJPROP_TEXT, infoText);
      ObjectSetInteger(0, infoName2, OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(0, infoName2, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, infoName2, OBJPROP_YDISTANCE, 15 + FontSize * 3); // 放在第一个标签下方
      ObjectSetInteger(0, infoName2, OBJPROP_FONTSIZE, FontSize);

      // 使用与第一个标签相同的颜色
      if(trendDirection == "看涨趋势")
         ObjectSetInteger(0, infoName2, OBJPROP_COLOR, BullishColor);
      else if(trendDirection == "看跌趋势")
         ObjectSetInteger(0, infoName2, OBJPROP_COLOR, BearishColor);
      else
         ObjectSetInteger(0, infoName2, OBJPROP_COLOR, clrWhite);
   }

   // 显示交易信息
   if(EnableTrading && d > 0)
   {
      string tradeInfoName = "TradeInfo";
      if(ObjectCreate(0, tradeInfoName, OBJ_LABEL, 0, 0, 0))
      {
         // 构建交易信息文本
         string tradeInfo = "交易设置:\n";
         tradeInfo += "初始档位: " + (initialLevel >= 0 ? IntegerToString(initialLevel + 1) : "无") + "\n";
         tradeInfo += "持仓状态: " + (hasOpenPosition ? "有持仓" : "无持仓") + "\n";

         // 设置标签属性
         ObjectSetString(0, tradeInfoName, OBJPROP_TEXT, tradeInfo);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_XDISTANCE, 10);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_YDISTANCE, 15);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_FONTSIZE, FontSize);
         ObjectSetInteger(0, tradeInfoName, OBJPROP_COLOR, clrYellow);
      }
   }

   // 刷新图表
   ChartRedraw(0);
}

//+------------------------------------------------------------------+
//| 绘制斐波那契线                                                    |
//+------------------------------------------------------------------+
void DrawFibonacciLines()
{
   // 回测模式非可视化时跳过绘图
   if(TestMode && !IsVisualMode()) return;

   if(dSource == 0) return; // 如果没有确定d值来源，则不绘制

   // 获取最新K线的时间
   datetime currentTime = Time[0];

   // 计算延伸线的结束时间（向右延伸一定距离）
   datetime endTime = currentTime + (currentTime - Time[10]) * 3;

   // 确定回测线的起点和终点
   datetime startTime, endTimePoint;
   double startLevel, endLevel;

   // 根据d值来源确定起点时间和价格水平
   if(dSource == 1) // d来源于d1 (z1-z2)
   {
      startTime = lastTurningPoints[0]; // 使用z1的时间作为起点
      endTimePoint = lastTurningPoints[1]; // z2的时间作为终点
      startLevel = lastTurningPrices[0]; // z1价格
      endLevel = lastTurningPrices[1];   // z2价格
   }
   else // d来源于d2 (z2-z3)
   {
      startTime = lastTurningPoints[1]; // 使用z2的时间作为起点
      endTimePoint = lastTurningPoints[2]; // z3的时间作为终点
      startLevel = lastTurningPrices[1]; // z2价格
      endLevel = lastTurningPrices[2];   // z3价格
   }

   // 计算价格范围
   double range = MathAbs(endLevel - startLevel);

   // 确定趋势方向
   bool isUptrend = (endLevel > startLevel);

   // 计算标签的位置 - 将标签放在更靠近图表中间的位置
   // 计算中间位置：起点时间和当前时间的中间点
   datetime labelTime = startTime + (currentTime - startTime) / 2;

   // 绘制斐波那契线
   for(int i = 0; i < ArraySize(fiboLevels); i++)
   {
      // 计算当前斐波那契水平的价格
      double fiboPrice;

      if(isUptrend) // 上升趋势
      {
         fiboPrice = startLevel + range * fiboLevels[i];
      }
      else // 下降趋势
      {
         fiboPrice = startLevel - range * fiboLevels[i];
      }

      // 创建水平线
      string lineName = "FiboLine_" + DoubleToString(fiboLevels[i], 3);

      if(ObjectCreate(0, lineName, OBJ_TREND, 0, startTime, fiboPrice, endTime, fiboPrice))
      {
         ObjectSetInteger(0, lineName, OBJPROP_COLOR, FiboLineColor);
         ObjectSetInteger(0, lineName, OBJPROP_STYLE, FiboLineStyle);
         ObjectSetInteger(0, lineName, OBJPROP_WIDTH, FiboLineWidth);
         ObjectSetInteger(0, lineName, OBJPROP_BACK, true);
         ObjectSetInteger(0, lineName, OBJPROP_SELECTABLE, false);
         ObjectSetInteger(0, lineName, OBJPROP_RAY_RIGHT, true); // 向右延伸
      }

      // 使用OBJ_TEXT直接在线上添加标签，放在图表中间位置
      string labelName = "FiboLabel_" + DoubleToString(fiboLevels[i], 3);
      if(ObjectCreate(0, labelName, OBJ_TEXT, 0, labelTime, fiboPrice))
      {
         // 构建标签文本 - 显示档位编号、斐波那契水平和价格
         string levelText = "第" + IntegerToString(i+1) + "档: " + DoubleToString(fiboLevels[i], 3) +
                           " (" + DoubleToString(fiboPrice, Digits) + ")";

         // 设置标签属性
         ObjectSetString(0, labelName, OBJPROP_TEXT, levelText);
         ObjectSetInteger(0, labelName, OBJPROP_COLOR, FiboLineColor);
         ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 9);
         ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT);
         ObjectSetInteger(0, labelName, OBJPROP_ZORDER, 2000); // 确保显示在最前面
      }

      // 在起点处添加额外的简洁标签
      string startLabelName = "FiboStartLabel_" + DoubleToString(fiboLevels[i], 3);
      if(ObjectCreate(0, startLabelName, OBJ_TEXT, 0, startTime, fiboPrice))
      {
         // 显示档位编号和斐波那契水平
         string levelText = IntegerToString(i+1) + ":" + DoubleToString(fiboLevels[i], 3);

         // 设置标签属性
         ObjectSetString(0, startLabelName, OBJPROP_TEXT, levelText);
         ObjectSetInteger(0, startLabelName, OBJPROP_COLOR, FiboLineColor);
         ObjectSetInteger(0, startLabelName, OBJPROP_FONTSIZE, 8);
         ObjectSetInteger(0, startLabelName, OBJPROP_ANCHOR, ANCHOR_RIGHT);
         ObjectSetInteger(0, startLabelName, OBJPROP_ZORDER, 2000); // 确保显示在最前面
      }
   }

   // 标记初始下单区域
   if(initialLevel >= 0 && initialLevel < ArraySize(fiboLevels))
   {
      // 计算初始下单档位对应的价格
      double fiboLevel = fiboLevels[initialLevel];
      double fiboPrice;

      if(isUptrend)
      {
         fiboPrice = startLevel + range * fiboLevel;
      }
      else
      {
         fiboPrice = startLevel - range * fiboLevel;
      }

      // 创建区域标记
      string rectName = "InitialOrderLevel";

      if(ObjectCreate(0, rectName, OBJ_ARROW, 0, startTime, fiboPrice))
      {
         ObjectSetInteger(0, rectName, OBJPROP_ARROWCODE, 159); // 圆形标记
         ObjectSetInteger(0, rectName, OBJPROP_COLOR, clrYellow);
         ObjectSetInteger(0, rectName, OBJPROP_WIDTH, 5);
         ObjectSetInteger(0, rectName, OBJPROP_SELECTABLE, false);
      }

      // 添加描述标签
      string initLabelName = "InitialLevelLabel";
      if(ObjectCreate(0, initLabelName, OBJ_TEXT, 0, startTime, fiboPrice))
      {
         string labelText = "初始下单位置: 第" + IntegerToString(initialLevel+1) +
                           "档 (" + DoubleToString(fiboLevels[initialLevel], 3) + ")";
         ObjectSetString(0, initLabelName, OBJPROP_TEXT, labelText);
         ObjectSetInteger(0, initLabelName, OBJPROP_COLOR, clrYellow);
         ObjectSetInteger(0, initLabelName, OBJPROP_FONTSIZE, 9);
         ObjectSetInteger(0, initLabelName, OBJPROP_ANCHOR, ANCHOR_RIGHT);
         ObjectSetInteger(0, initLabelName, OBJPROP_ZORDER, 3000);
      }
   }

   // 添加一个额外的描述标签，说明这是斐波那契回测线
   string infoLabelName = "FiboInfo";
   if(ObjectCreate(0, infoLabelName, OBJ_LABEL, 0, 0, 0))
   {
      string infoText = "斐波那契回测线 (" + (isUptrend ? "上升" : "下降") + "趋势)";
      if(dSource == 1)
         infoText += " [z1→z2]";
      else
         infoText += " [z2→z3]";

      ObjectSetString(0, infoLabelName, OBJPROP_TEXT, infoText);
      ObjectSetInteger(0, infoLabelName, OBJPROP_COLOR, FiboLineColor);
      ObjectSetInteger(0, infoLabelName, OBJPROP_FONTSIZE, 8);
      ObjectSetInteger(0, infoLabelName, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, infoLabelName, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, infoLabelName, OBJPROP_YDISTANCE, 5);
   }

   // 绘制起点到终点的连接线，清晰显示波动方向
   string waveLineName = "FiboWaveLine";
   if(ObjectCreate(0, waveLineName, OBJ_TREND, 0, startTime, startPrice, endTimePoint, endPrice))
   {
      color waveColor = isUptrend ? BullishColor : BearishColor;
      ObjectSetInteger(0, waveLineName, OBJPROP_COLOR, waveColor);
      ObjectSetInteger(0, waveLineName, OBJPROP_WIDTH, 2);
      ObjectSetInteger(0, waveLineName, OBJPROP_STYLE, STYLE_SOLID);
      ObjectSetInteger(0, waveLineName, OBJPROP_RAY_RIGHT, false);
      ObjectSetInteger(0, waveLineName, OBJPROP_BACK, false);
   }
}

//+------------------------------------------------------------------+
//| 重置交易状态                                                      |
//+------------------------------------------------------------------+
void ResetTradeStatus()
{
   lastDChange = TimeCurrent();

   // 重置回本功能相关变量
   originalStopLoss = 0;
   originalOpenPrice = 0;
   originalLotSize = 0;
   originalOrderType = -1;
   isBreakEvenOrder = false;

   // 检查是否有与当前EA相关的持仓单
   hasOpenPosition = false;
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            hasOpenPosition = true;
            break;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 确定初始下单挡位                                                  |
//+------------------------------------------------------------------+
void DetermineInitialLevel()
{
   // 根据d值确定初始下单挡位
   // 使用输入参数设置的阈值和档位

   double pointsMultiplier = 1;
   if(Digits == 3 || Digits == 5) // JPY对或微型账户
      pointsMultiplier = 10;

   // 直接使用d值，不进行复杂的点数转换
   // 这样可以避免因为不同货币对的Digits值不同导致的问题
   double dPoints = d;

   int oldInitialLevel = initialLevel; // 保存旧的初始下单挡位

   if(dPoints > D_Value_High)
   {
      initialLevel = Initial_Level_High; // 默认为第三档
   }
   else if(dPoints > D_Value_Mid)
   {
      initialLevel = Initial_Level_Mid; // 默认为第四档
   }
   else if(dPoints > D_Value_Low)
   {
      initialLevel = Initial_Level_Low; // 默认为第五档
   }
   // 新增条件 - 处理10<d<20的情况
   else if(dPoints > D_Value_VeryLow)
   {
      initialLevel = Initial_Level_VeryLow; // 设置为第六档
   }
   else if(dPoints > D_Value_SuperLow)
   {
      initialLevel = Initial_Level_SuperLow; // 设置为第七档
   }
   else
   {
      initialLevel = -1; // d值太小，不进行交易
   }

   // 如果初始下单挡位发生变化，重新绘制图表
   if(initialLevel != oldInitialLevel)
   {
      if(!TestMode)
         Print("初始下单挡位变化: ", (oldInitialLevel >= 0 ? IntegerToString(oldInitialLevel+1) : "无"),
               " -> ", (initialLevel >= 0 ? IntegerToString(initialLevel+1) : "无"));

      // 清除旧的标记
      ObjectsDeleteAll(0, "InitialOrderLevel");
      ObjectsDeleteAll(0, "InitialLevelLabel");

      // 重新绘制斐波那契线和初始下单区域
      if(ShowFiboLines && dSource > 0 && (!TestMode || IsVisualMode()))
      {
         MarkTurningPoints(); // 重新绘制所有标记
      }
   }
}

//+------------------------------------------------------------------+
//| 检查价格是否在已交易的区域内                                     |
//+------------------------------------------------------------------+
bool IsPriceInTradedZone(double price)
{
   for(int i = 0; i < tradedPriceZonesCount; i++)
   {
      if(price >= tradedPriceZones[i][0] && price <= tradedPriceZones[i][1])
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 添加一个已交易的价格区间                                         |
//+------------------------------------------------------------------+
void AddTradedPriceZone(double zoneStartPrice, double zoneEndPrice)
{
   // 确保记录不超过最大限制
   if(tradedPriceZonesCount >= MAX_PRICE_MEMORY)
   {
      // 移除最旧的记录
      for(int i = 0; i < MAX_PRICE_MEMORY - 1; i++)
      {
         tradedPriceZones[i][0] = tradedPriceZones[i+1][0];
         tradedPriceZones[i][1] = tradedPriceZones[i+1][1];
      }
      tradedPriceZonesCount = MAX_PRICE_MEMORY - 1;
   }

   // 添加新记录
   tradedPriceZones[tradedPriceZonesCount][0] = zoneStartPrice;
   tradedPriceZones[tradedPriceZonesCount][1] = zoneEndPrice;
   tradedPriceZonesCount++;

   if(!TestMode)
      Print("添加已交易区域: ", zoneStartPrice, " - ", zoneEndPrice);
}

//+------------------------------------------------------------------+
//| 清除所有已交易区域记录                                           |
//+------------------------------------------------------------------+
void ClearTradedPriceZones()
{
   tradedPriceZonesCount = 0;
   if(!TestMode)
      Print("已清除所有交易区域记录");
}

//+------------------------------------------------------------------+
//| 新的交易管理逻辑                                                  |
//+------------------------------------------------------------------+
void ManageSimpleTrading()
{
   // 如果没有有效的初始挡位或已有持仓，不进行交易
   if(initialLevel < 0 || hasOpenPosition)
      return;

   // 计算初始档位对应的斐波那契价格
   double range = MathAbs(endPrice - startPrice);
   bool isUptrend = (trendDirection == "看涨趋势");
   double fiboLevel = fiboLevels[initialLevel];
   double fiboPrice;

   // 根据趋势方向计算斐波那契水平对应的价格
   if(isUptrend)
   {
      // 上升趋势，斐波那契回调价格 = 终点价格 - 范围 * 斐波那契水平
      fiboPrice = endPrice - range * fiboLevel;
   }
   else
   {
      // 下降趋势，斐波那契回调价格 = 终点价格 + 范围 * 斐波那契水平
      fiboPrice = endPrice + range * fiboLevel;
   }

   // 检查该价格点位是否已交易过
   if(IsPriceInTradedZone(fiboPrice))
   {
      return; // 已交易过该点位，跳过
   }

   // 检查新的交易条件
   if(CheckNewTradingConditions(fiboPrice, isUptrend))
   {
      // 执行下单
      bool orderResult = PlaceOrderWithStopLoss(isUptrend);

      if(orderResult)
      {
         // 记录该价格点位已交易
         double zoneTolerance = Point * 10;
         AddTradedPriceZone(fiboPrice - zoneTolerance, fiboPrice + zoneTolerance);

         // 输出日志
         if(!TestMode)
            Print("满足新交易条件，在斐波那契点位 ", fiboLevel, " (", fiboPrice, ") 下单成功，档位: 第", initialLevel+1, "档");
      }
   }
}

//+------------------------------------------------------------------+
//| 检查新的交易条件                                                  |
//+------------------------------------------------------------------+
bool CheckNewTradingConditions(double fiboPrice, bool isUptrend)
{
   // 确保有足够的K线数据
   if(Bars < 4) return false;

   if(isUptrend) // 看涨趋势
   {
      // 检查上上柱k线或上柱k线最低点小于初始挡位的值
      double bar2Low = Low[2]; // 上上柱最低点
      double bar1Low = Low[1]; // 上柱最低点

      bool lowCondition = (bar2Low < fiboPrice) || (bar1Low < fiboPrice);

      if(!lowCondition) return false;

      // 检查上上上柱为阴线
      bool bar3Bearish = (Close[3] < Open[3]);

      if(!bar3Bearish) return false;

      // 检查上上柱和上柱k线为阳线
      bool bar2Bullish = (Close[2] > Open[2]);
      bool bar1Bullish = (Close[1] > Open[1]);

      return (bar2Bullish && bar1Bullish);
   }
   else // 看跌趋势
   {
      // 检查上上柱k线或上柱k线最高点大于初始挡位的值
      double bar2High = High[2]; // 上上柱最高点
      double bar1High = High[1]; // 上柱最高点

      bool highCondition = (bar2High > fiboPrice) || (bar1High > fiboPrice);

      if(!highCondition) return false;

      // 检查上上上柱为阳线
      bool bar3Bullish = (Close[3] > Open[3]);

      if(!bar3Bullish) return false;

      // 检查上上柱和上柱k线为阴线
      bool bar2Bearish = (Close[2] < Open[2]);
      bool bar1Bearish = (Close[1] < Open[1]);

      return (bar2Bearish && bar1Bearish);
   }
}

//+------------------------------------------------------------------+
//| 带止损的下单函数                                                  |
//+------------------------------------------------------------------+
bool PlaceOrderWithStopLoss(bool isUptrend)
{
   int orderType;
   double price;
   string comment;
   double stopLoss;
   double takeProfit;

   // 计算前三柱最低点/最高点
   double extremePrice;
   if(isUptrend)
   {
      // 多单：计算前三柱最低点
      extremePrice = MathMin(MathMin(Low[1], Low[2]), Low[3]);
      stopLoss = extremePrice - SLOffset * Point;

      orderType = OP_BUY;
      price = Ask;
      takeProfit = price + DefaultTP * Point;
      comment = "ZigZag看涨K线形态 档位:" + IntegerToString(initialLevel+1);
   }
   else
   {
      // 空单：计算前三柱最高点
      extremePrice = MathMax(MathMax(High[1], High[2]), High[3]);
      stopLoss = extremePrice + SLOffset * Point;

      orderType = OP_SELL;
      price = Bid;
      takeProfit = price - DefaultTP * Point;
      comment = "ZigZag看跌K线形态 档位:" + IntegerToString(initialLevel+1);
   }

   // 执行下单（设置止盈止损）
   int ticket = OrderSend(Symbol(), orderType, LotSize, price, Slippage, stopLoss, takeProfit, comment, MagicNumber);

   if(ticket > 0)
   {
      if(!TestMode)
         Print("订单执行成功: ", comment, " 手数: ", LotSize, " 价格: ", price,
               " 止损: ", stopLoss, " 止盈: ", takeProfit);
      hasOpenPosition = true;

      // 如果启用回本功能且这不是回本单，记录首单信息
      if(EnableBreakEven && !isBreakEvenOrder)
      {
         originalStopLoss = stopLoss;
         originalOpenPrice = price;
         originalLotSize = LotSize;
         originalOrderType = orderType;
         if(!TestMode)
            Print("记录首单信息 - 开仓价: ", originalOpenPrice, " 止损: ", originalStopLoss, " 手数: ", originalLotSize);
      }

      return true;
   }
   else
   {
      int error = GetLastError();
      if(!TestMode)
         Print("订单执行失败: 错误代码", error, " 手数: ", LotSize, " 价格: ", price);
      return false;
   }
}

//+------------------------------------------------------------------+
//| 移动止损检查                                                      |
//+------------------------------------------------------------------+
void CheckTrailingStop()
{
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            double profitThreshold = d * 0.1; // 盈利阈值：0.1*d
            double newStopLoss = 0;
            bool shouldModify = false;

            if(OrderType() == OP_BUY) // 多单
            {
               double lastBarLow = Low[1]; // 上一柱最低点
               double currentProfit = (Bid - OrderOpenPrice()) / Point;

               // 检查是否盈利1*0.1d点
               if(currentProfit >= profitThreshold)
               {
                  newStopLoss = lastBarLow - TrailingOffset * Point;

                  // 确保新止损比当前止损更有利
                  if(newStopLoss > OrderStopLoss() + Point)
                  {
                     shouldModify = true;
                  }
               }
            }
            else if(OrderType() == OP_SELL) // 空单
            {
               double lastBarHigh = High[1]; // 上一柱最高点
               double currentProfit = (OrderOpenPrice() - Ask) / Point;

               // 检查是否盈利1*0.1d点
               if(currentProfit >= profitThreshold)
               {
                  newStopLoss = lastBarHigh + TrailingOffset * Point;

                  // 确保新止损比当前止损更有利
                  if(newStopLoss < OrderStopLoss() - Point || OrderStopLoss() == 0)
                  {
                     shouldModify = true;
                  }
               }
            }

            if(shouldModify)
            {
               bool result = OrderModify(OrderTicket(), OrderOpenPrice(), newStopLoss, OrderTakeProfit(), 0);
               if(result && !TestMode)
               {
                  Print("移动止损成功: 订单", OrderTicket(), " 新止损: ", newStopLoss);
               }
               else if(!result && !TestMode)
               {
                  Print("移动止损失败: 订单", OrderTicket(), " 错误: ", GetLastError());
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检查止损是否触发                                                  |
//+------------------------------------------------------------------+
void CheckStopLossTriggered()
{
   // 检查当前价格是否触发原始止损
   bool stopLossTriggered = false;
   double currentPrice = 0;

   if(originalOrderType == OP_BUY)
   {
      currentPrice = Bid;
      stopLossTriggered = (currentPrice <= originalStopLoss);
   }
   else if(originalOrderType == OP_SELL)
   {
      currentPrice = Ask;
      stopLossTriggered = (currentPrice >= originalStopLoss);
   }

   if(stopLossTriggered)
   {
      if(!TestMode)
         Print("检测到止损触发，当前价格: ", currentPrice, " 原始止损: ", originalStopLoss);

      // 等待当前持仓被止损平仓
      Sleep(100); // 等待100毫秒

      // 检查持仓是否已被平仓
      bool positionClosed = true;
      for(int i = OrdersTotal() - 1; i >= 0; i--)
      {
         if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
         {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
            {
               positionClosed = false;
               break;
            }
         }
      }

      if(positionClosed)
      {
         // 持仓已平仓，下回本单
         PlaceBreakEvenOrder(currentPrice);

         // 重置状态
         hasOpenPosition = false;
         isBreakEvenOrder = true;
      }
   }
}

//+------------------------------------------------------------------+
//| 下回本单                                                          |
//+------------------------------------------------------------------+
void PlaceBreakEvenOrder(double triggerPrice)
{
   // 确保有足够的K线数据
   if(Bars < 4) return;

   int newOrderType;
   double newPrice;
   double newStopLoss;
   double newTakeProfit = 0;
   string comment;

   // 计算亏损点数
   double lossPoints = MathAbs(originalOpenPrice - originalStopLoss) / Point;

   if(originalOrderType == OP_BUY) // 原来是多单，现在下空单
   {
      newOrderType = OP_SELL;
      newPrice = Bid;

      // 止损为前三柱最高点 + 偏移
      double extremePrice = MathMax(MathMax(High[1], High[2]), High[3]);
      newStopLoss = extremePrice + SLOffset * Point;

      // 止盈计算：当前价格 - 亏损点数
      if(EnableBreakEvenTP)
      {
         newTakeProfit = triggerPrice - lossPoints * Point;
      }

      comment = "ZigZag回本空单 亏损:" + DoubleToString(lossPoints, 1) + "点";
   }
   else // 原来是空单，现在下多单
   {
      newOrderType = OP_BUY;
      newPrice = Ask;

      // 止损为前三柱最低点 - 偏移
      double extremePrice = MathMin(MathMin(Low[1], Low[2]), Low[3]);
      newStopLoss = extremePrice - SLOffset * Point;

      // 止盈计算：当前价格 + 亏损点数
      if(EnableBreakEvenTP)
      {
         newTakeProfit = triggerPrice + lossPoints * Point;
      }

      comment = "ZigZag回本多单 亏损:" + DoubleToString(lossPoints, 1) + "点";
   }

   // 执行回本单
   int ticket = OrderSend(Symbol(), newOrderType, originalLotSize, newPrice, Slippage, newStopLoss, newTakeProfit, comment, MagicNumber);

   if(ticket > 0)
   {
      if(!TestMode)
      {
         Print("回本单执行成功: ", comment);
         Print("手数: ", originalLotSize, " 价格: ", newPrice, " 止损: ", newStopLoss);
         if(EnableBreakEvenTP)
            Print("止盈: ", newTakeProfit, " (回本", lossPoints, "点)");
         else
            Print("未设置止盈");
      }
      hasOpenPosition = true;
   }
   else
   {
      int error = GetLastError();
      if(!TestMode)
         Print("回本单执行失败: 错误代码", error, " 手数: ", originalLotSize, " 价格: ", newPrice);
      isBreakEvenOrder = false; // 下单失败，重置标志
   }
}
